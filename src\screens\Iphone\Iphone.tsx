import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "../../components/ui/card";
import { Button } from "../../components/ui/button";

export const Iphone = (): JSX.Element => {
  // Air quality metrics data
  const airQualityMetrics = [
    {
      value: "014",
      label: "PM2.5(µg/m³）",
      width: "w-[73px]",
    },
    {
      value: "36",
      label: "甲醛(µg/m³）",
      width: "w-[65px]",
    },
    {
      value: "25500",
      label: "负氧离子(个/cm³）",
      width: "w-[88px]",
    },
  ];

  // Outdoor air quality data for comparison
  const outdoorAirQualityData = [
    {
      label: "车外PM2.5",
      value: "105",
      color: "text-orange-500",
    },
    {
      label: "车内PM2.5",
      value: "14",
      color: "text-green-500",
    },
    {
      label: "车外负氧离子",
      value: "628",
      color: "text-green-600",
    },
  ];

  return (
    <div
      className="bg-neutral-100 flex flex-row justify-center w-full"
      data-model-id="107:9"
    >
      <div className="bg-neutral-100 w-[393px] h-[852px] relative">
        {/* Status bar */}
        <div className="absolute w-[375px] h-11 top-0 left-[11px] flex items-center justify-between px-4">
          <div className="text-black text-sm font-medium">9:41</div>
          <div className="flex items-center gap-1">
            <div className="flex gap-1">
              <div className="w-1 h-1 bg-black rounded-full"></div>
              <div className="w-1 h-1 bg-black rounded-full"></div>
              <div className="w-1 h-1 bg-black rounded-full"></div>
              <div className="w-1 h-1 bg-black rounded-full"></div>
            </div>
            <div className="w-4 h-2 border border-black rounded-sm">
              <div className="w-3 h-1 bg-blue-500 rounded-sm m-0.5"></div>
            </div>
            <div className="w-6 h-3 border border-black rounded-sm bg-green-500"></div>
          </div>
        </div>

        {/* Add Device Button */}
        <Button className="absolute top-[60px] right-[20px] w-12 h-12 rounded-full bg-teal-600 hover:bg-teal-700 text-white p-0 flex flex-col items-center justify-center text-xs">
          <span className="text-[10px] leading-tight">添加</span>
          <span className="text-[10px] leading-tight">设备</span>
        </Button>

        <div className="absolute w-[393px] h-[783px] top-[69px] left-0">
          {/* Health protection days - moved to top */}
          <div className="absolute top-[15px] left-0 w-full text-center">
            <span className="text-[#5b5b5b] text-[14px] [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal">已为您健康守护 </span>
            <span className="text-[#5b5b5b] text-[18px] [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal">231</span>
            <span className="text-[#5b5b5b] text-[14px] [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal"> 天</span>
          </div>

          {/* Cleaning reminder setting - moved to top */}
          <div className="absolute top-[38px] left-0 w-full text-center">
            <span className="text-[#5b5b5b] text-[14px] [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal">设置 </span>
            <span className="text-[#ff8800] text-[14px] [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal">清洗提醒</span>
          </div>

          {/* Air quality metrics - moved to upper position */}
          <div className="absolute w-[350px] h-[70px] top-[70px] left-[22px] flex justify-between">
            {airQualityMetrics.map((metric, index) => (
              <div key={index} className="h-[70px] flex-1 flex flex-col items-center justify-center">
                <div className="[font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#494949] text-[28px] tracking-[0] leading-[normal] whitespace-nowrap">
                  {metric.value}
                </div>
                <div className="[font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal text-[#909090] text-[10px] tracking-[0] leading-[normal] whitespace-nowrap text-center mt-1">
                  {metric.label}
                </div>
              </div>
            ))}
          </div>

          {/* Colorful arc decoration - simplified design */}
          <div className="absolute w-full top-[160px] left-0 flex justify-center">
            <div className="relative w-[300px] h-[150px]">
              {/* Colorful gradient arc */}
              <svg width="300" height="150" viewBox="0 0 300 150" className="absolute">
                <defs>
                  <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#ef4444" />
                    <stop offset="25%" stopColor="#f97316" />
                    <stop offset="50%" stopColor="#eab308" />
                    <stop offset="75%" stopColor="#22c55e" />
                    <stop offset="100%" stopColor="#06b6d4" />
                  </linearGradient>
                </defs>
                <path
                  d="M 50 140 A 100 100 0 0 1 250 140"
                  stroke="url(#arcGradient)"
                  strokeWidth="8"
                  fill="none"
                  strokeLinecap="round"
                />
              </svg>
            </div>
          </div>

          {/* Air quality indicator - moved to center */}
          <div className="absolute top-[200px] left-0 w-full flex flex-col items-center">
            <div className="text-[#454545] text-[12px] [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal mb-1">
              车内综合空气质量
            </div>
            <div className="text-[#454545] text-xl [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal mb-2">
              空气优
            </div>
            <div className="text-[#1b705f] text-6xl [font-family:'HarmonyOS_Sans-Regular',Helvetica] font-normal">
              25
            </div>
          </div>

          {/* Car visualization - moved to center */}
          <div className="absolute top-[320px] left-0 w-full flex justify-center">
            <img
              className="w-[280px] h-[160px] object-cover"
              alt="Car visualization"
              src="https://c.animaapp.com/mdqqarhondzElL/img/---2.png"
            />
          </div>



          {/* Weather information card - detailed */}
          <Card className="absolute top-[500px] left-[22px] w-[350px] bg-white rounded-xl shadow-sm border border-gray-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-2xl font-bold text-black [font-family:'HarmonyOS_Sans-Regular',Helvetica]">深圳市</h3>
                  <p className="text-sm text-gray-500 [font-family:'HarmonyOS_Sans-Regular',Helvetica]">2024-06-09 15:07 更新</p>
                </div>
                <div className="text-right">
                  <div className="text-4xl font-bold text-black [font-family:'HarmonyOS_Sans-Regular',Helvetica]">33°</div>
                </div>
              </div>

              <div className="space-y-2 text-sm [font-family:'HarmonyOS_Sans-Regular',Helvetica]">
                <div className="flex justify-between">
                  <span className="text-gray-600">空气质量:</span>
                  <span className="text-black">中</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">湿度:</span>
                  <span className="text-black">52%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">距离:</span>
                  <span className="text-black">36km</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Outdoor air quality comparison */}
          <div className="absolute top-[650px] left-[22px] w-[350px] flex justify-between">
            {outdoorAirQualityData.map((data, index) => (
              <div key={index} className="text-center">
                <div className="text-sm text-gray-600 [font-family:'HarmonyOS_Sans-Regular',Helvetica] mb-1">
                  {data.label}
                </div>
                <div className={`text-2xl font-bold [font-family:'HarmonyOS_Sans-Regular',Helvetica] ${data.color}`}>
                  {data.value}
                </div>
              </div>
            ))}
          </div>

          {/* Refresh button */}
          <div className="absolute bottom-[20px] right-[20px]">
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-400 hover:text-gray-600 [font-family:'HarmonyOS_Sans-Regular',Helvetica]"
            >
              刷新
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
