<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'HarmonyOS Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #f5f5f5;
            border-radius: 25px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        /* Status Bar */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #000;
            font-size: 14px;
            font-weight: 500;
        }

        .status-icons {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .signal-dots {
            display: flex;
            gap: 2px;
        }

        .dot {
            width: 4px;
            height: 4px;
            background: #000;
            border-radius: 50%;
        }

        .wifi-icon, .battery-icon {
            width: 20px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery-icon::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #4ade80;
            border-radius: 1px;
        }

        /* Add Device Button */
        .add-device-btn {
            position: absolute;
            top: 60px;
            right: 20px;
            width: 48px;
            height: 48px;
            background: #0d9488;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            line-height: 1.2;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 8px rgba(13, 148, 136, 0.3);
            transition: all 0.3s ease;
        }

        .add-device-btn:hover {
            background: #0f766e;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(13, 148, 136, 0.4);
        }

        .add-device-btn:active {
            transform: scale(0.95);
        }

        /* Main Content */
        .main-content {
            position: absolute;
            top: 69px;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 0 20px;
        }

        /* Arc Decoration - moved to top */
        .arc-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* Air Quality Score */
        .air-quality-score {
            text-align: center;
            margin-bottom: 30px;
        }

        .score-label {
            font-size: 12px;
            color: #454545;
            margin-bottom: 5px;
        }

        .score-status {
            font-size: 20px;
            color: #454545;
            margin-bottom: 10px;
        }

        .score-number {
            font-size: 60px;
            color: #1b705f;
            font-weight: 500;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Car Image */
        .car-container {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .car-image {
            width: 280px;
            height: 160px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 200"><defs><linearGradient id="carGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%2306b6d4"/><stop offset="100%" style="stop-color:%2322c55e"/></linearGradient></defs><path d="M50 120 Q50 100 70 100 L120 100 Q140 80 160 80 L240 80 Q260 80 280 100 L330 100 Q350 100 350 120 L350 140 Q350 160 330 160 L320 160 Q300 160 300 140 Q300 120 280 120 L120 120 Q100 120 100 140 Q100 160 80 160 L70 160 Q50 160 50 140 Z" fill="url(%23carGrad)"/><circle cx="120" cy="140" r="20" fill="%23374151"/><circle cx="280" cy="140" r="20" fill="%23374151"/><circle cx="120" cy="140" r="12" fill="%23d1d5db"/><circle cx="280" cy="140" r="12" fill="%23d1d5db"/><rect x="160" y="90" width="80" height="30" rx="5" fill="rgba(255,255,255,0.3)"/></svg>') center/contain no-repeat;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .car-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Health Protection Info - moved after car */
        .health-info {
            text-align: center;
            margin-bottom: 25px;
        }

        .health-days {
            color: #5b5b5b;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .health-days .number {
            font-size: 18px;
            font-weight: 500;
        }

        .clean-reminder {
            color: #5b5b5b;
            font-size: 14px;
        }

        .clean-reminder .highlight {
            color: #ff8800;
        }

        /* Air Quality Metrics - moved after health info */
        .air-metrics {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 0 10px;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 500;
            color: #494949;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 10px;
            color: #909090;
            line-height: 1.2;
        }

        /* Weather Card */
        .weather-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            padding: 16px;
            margin: 0 2px 20px 2px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .weather-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .weather-location h3 {
            font-size: 24px;
            font-weight: bold;
            color: #000;
            margin-bottom: 4px;
        }

        .weather-time {
            font-size: 12px;
            color: #666;
        }

        .weather-temp {
            font-size: 36px;
            font-weight: bold;
            color: #000;
        }

        .weather-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .weather-row {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }

        .weather-label {
            color: #666;
        }

        .weather-value {
            color: #000;
        }

        /* Outdoor Air Quality */
        .outdoor-metrics {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 0 10px;
        }

        .outdoor-item {
            text-align: center;
            flex: 1;
        }

        .outdoor-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .outdoor-value {
            font-size: 24px;
            font-weight: bold;
        }

        .outdoor-value.orange {
            color: #f97316;
        }

        .outdoor-value.green {
            color: #22c55e;
        }

        .outdoor-value.green-dark {
            color: #16a34a;
        }

        /* Refresh Button */
        .refresh-btn {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: none;
            border: none;
            color: #999;
            font-size: 12px;
            cursor: pointer;
            padding: 8px;
        }

        .refresh-btn:hover {
            color: #666;
        }

        /* Device Status */
        .device-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 16px;
            margin: 0 2px 20px 2px;
        }

        .status-item {
            text-align: center;
            flex: 1;
        }

        .status-item.center {
            display: flex;
            justify-content: center;
        }

        .status-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .status-value {
            font-size: 14px;
            color: #000;
            font-weight: 500;
        }

        .power-button {
            width: 40px;
            height: 40px;
            background: #ff8800;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .power-button:hover {
            background: #e67700;
            transform: scale(1.05);
        }

        .power-icon {
            width: 20px;
            height: 20px;
            border: 2px solid white;
            border-radius: 50%;
            position: relative;
        }

        .power-icon::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 10px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="time">9:41</div>
            <div class="status-icons">
                <div class="signal-dots">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
                <div class="wifi-icon">
                    <div style="width: 60%; height: 100%; background: #007AFF; border-radius: 1px;"></div>
                </div>
                <div class="battery-icon">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- Add Device Button -->
        <button class="add-device-btn">
            <span>添加</span>
            <span>设备</span>
        </button>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Arc Decoration - moved to top -->
            <div class="arc-container">
                <svg width="300" height="150" viewBox="0 0 300 150">
                    <defs>
                        <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stop-color="#ef4444" />
                            <stop offset="25%" stop-color="#f97316" />
                            <stop offset="50%" stop-color="#eab308" />
                            <stop offset="75%" stop-color="#22c55e" />
                            <stop offset="100%" stop-color="#06b6d4" />
                        </linearGradient>
                    </defs>
                    <path d="M 50 140 A 100 100 0 0 1 250 140"
                          stroke="url(#arcGradient)"
                          stroke-width="8"
                          fill="none"
                          stroke-linecap="round" />
                </svg>
            </div>

            <!-- Air Quality Score -->
            <div class="air-quality-score">
                <div class="score-label">车内综合空气质量</div>
                <div class="score-status">空气优</div>
                <div class="score-number">25</div>
            </div>

            <!-- Car Image -->
            <div class="car-container">
                <div class="car-image"></div>
            </div>

            <!-- Health Protection Info -->
            <div class="health-info">
                <div class="health-days">
                    已为您健康守护 <span class="number">231</span> 天
                </div>
                <div class="clean-reminder">
                    设置 <span class="highlight">清洗提醒</span>
                </div>
            </div>

            <!-- Air Quality Metrics -->
            <div class="air-metrics">
                <div class="metric-item">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(µg/m³）</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(µg/m³）</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³）</div>
                </div>
            </div>

            <!-- Weather Information Card -->
            <div class="weather-card">
                <div class="weather-header">
                    <div class="weather-location">
                        <h3>深圳市</h3>
                        <div class="weather-time">2024-06-09 15:07 更新</div>
                    </div>
                    <div class="weather-temp">33°</div>
                </div>

                <div class="weather-details">
                    <div class="weather-row">
                        <span class="weather-label">空气质量:</span>
                        <span class="weather-value">中</span>
                    </div>
                    <div class="weather-row">
                        <span class="weather-label">湿度:</span>
                        <span class="weather-value">52%</span>
                    </div>
                    <div class="weather-row">
                        <span class="weather-label">距离:</span>
                        <span class="weather-value">36km</span>
                    </div>
                </div>
            </div>

            <!-- Outdoor Air Quality Comparison -->
            <div class="outdoor-metrics">
                <div class="outdoor-item">
                    <div class="outdoor-label">车外PM2.5</div>
                    <div class="outdoor-value orange">105</div>
                </div>
                <div class="outdoor-item">
                    <div class="outdoor-label">车内PM2.5</div>
                    <div class="outdoor-value green">14</div>
                </div>
                <div class="outdoor-item">
                    <div class="outdoor-label">车外负氧离子</div>
                    <div class="outdoor-value green-dark">628</div>
                </div>
            </div>

            <!-- Device Status -->
            <div class="device-status">
                <div class="status-item">
                    <div class="status-label">设备运转</div>
                    <div class="status-value">正常</div>
                </div>
                <div class="status-item center">
                    <div class="power-button">
                        <div class="power-icon"></div>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-label">档位</div>
                    <div class="status-value">高</div>
                </div>
            </div>
        </div>

        <!-- Refresh Button -->
        <button class="refresh-btn">刷新</button>
    </div>

    <script>
        // Add some interactivity
        document.querySelector('.add-device-btn').addEventListener('click', function() {
            alert('添加设备功能');
        });

        document.querySelector('.refresh-btn').addEventListener('click', function() {
            // Simulate data refresh
            this.textContent = '刷新中...';
            setTimeout(() => {
                this.textContent = '刷新';
                // Here you could update the data
            }, 1000);
        });

        // Add click effect to clean reminder
        document.querySelector('.highlight').addEventListener('click', function() {
            alert('设置清洗提醒');
        });
    </script>
</body>
</html>
