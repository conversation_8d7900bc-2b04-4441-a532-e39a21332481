<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车载空气质量监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'HarmonyOS Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 25px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        /* Status Bar */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            z-index: 100;
        }

        .time {
            font-size: 17px;
            font-weight: 600;
            color: #000;
        }

        .battery-info {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
        }

        .bar {
            width: 3px;
            height: 4px;
            background: #000;
            border-radius: 1px;
        }

        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .wifi-icon, .battery-icon {
            width: 15px;
            height: 11px;
            background: #000;
            border-radius: 2px;
            position: relative;
        }

        .wifi-icon::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: #fff;
            border-radius: 1px;
        }

        /* Add Device Button */
        .add-device-btn {
            position: absolute;
            top: 60px;
            right: 20px;
            width: 48px;
            height: 48px;
            background: #0d9488;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            line-height: 1.2;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 8px rgba(13, 148, 136, 0.3);
            transition: all 0.3s ease;
        }

        .add-device-btn:hover {
            background: #0f766e;
            transform: scale(1.05);
        }

        /* Main Content */
        .main-content {
            position: absolute;
            top: 69px;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 0 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            overflow-y: auto;
        }

        /* Arc with Score Container */
        .arc-score-container {
            position: relative;
            margin: 30px 0 40px 0;
            width: 300px;
            height: 200px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .arc-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .air-quality-score {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -30%);
            text-align: center;
            z-index: 10;
        }

        .score-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
            font-weight: 400;
        }

        .score-status {
            font-size: 16px;
            color: #333;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .score-number {
            font-size: 48px;
            font-weight: 600;
            color: #22c55e;
            line-height: 1;
        }

        /* Car Image */
        .car-container {
            margin-bottom: 30px;
        }

        .car-image {
            width: 280px;
            height: 140px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 200"><defs><linearGradient id="carGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%2300d4aa"/><stop offset="50%" style="stop-color:%2300bcd4"/><stop offset="100%" style="stop-color:%2306b6d4"/></linearGradient><linearGradient id="windowGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23e0f2fe"/><stop offset="100%" style="stop-color:%23b3e5fc"/></linearGradient></defs><ellipse cx="200" cy="180" rx="180" ry="15" fill="rgba(0,0,0,0.1)"/><path d="M80 140 Q80 120 100 120 L140 120 Q160 100 180 100 L220 100 Q240 100 260 120 L300 120 Q320 120 320 140 L320 150 Q320 170 300 170 L290 170 Q270 170 270 150 Q270 130 250 130 L130 130 Q110 130 110 150 Q110 170 90 170 L80 170 Q60 170 60 150 L60 140 Q60 120 80 120 Z" fill="url(%23carGrad)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/><circle cx="130" cy="150" r="18" fill="%23374151"/><circle cx="270" cy="150" r="18" fill="%23374151"/><circle cx="130" cy="150" r="10" fill="%23d1d5db"/><circle cx="270" cy="150" r="10" fill="%23d1d5db"/><path d="M160 110 Q160 105 165 105 L235 105 Q240 105 240 110 L240 125 Q240 130 235 130 L165 130 Q160 130 160 125 Z" fill="url(%23windowGrad)" stroke="rgba(255,255,255,0.5)" stroke-width="0.5"/><circle cx="110" cy="135" r="3" fill="%23fbbf24"/><circle cx="290" cy="135" r="3" fill="%23ef4444"/></svg>') center/contain no-repeat;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        /* Health Protection Info */
        .health-info {
            text-align: center;
            margin-bottom: 25px;
        }

        .health-days {
            color: #5b5b5b;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .health-days .number {
            font-size: 18px;
            font-weight: 500;
        }

        .clean-reminder {
            color: #5b5b5b;
            font-size: 14px;
        }

        .clean-reminder .highlight {
            color: #ff8800;
        }

        /* Air Quality Metrics */
        .air-metrics {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 0 10px;
            width: 100%;
        }

        .metric-item {
            text-align: center;
            flex: 1;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 500;
            color: #494949;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 10px;
            color: #909090;
            line-height: 1.2;
        }

        /* Weather Card */
        .weather-card {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            width: 100%;
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .weather-left {
            display: flex;
            flex-direction: column;
        }

        .weather-city {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .weather-update {
            font-size: 10px;
            color: #999;
        }

        .weather-temp {
            font-size: 32px;
            font-weight: 600;
            color: #333;
        }

        .weather-temp::after {
            content: '°';
            font-size: 16px;
            vertical-align: top;
        }

        .weather-details {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
        }

        .weather-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }

        .weather-label {
            color: #999;
        }

        .weather-value {
            color: #333;
            font-weight: 500;
        }

        /* Outdoor Air Quality Comparison */
        .outdoor-metrics {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            width: 100%;
        }

        .outdoor-item {
            text-align: center;
            flex: 1;
        }

        .outdoor-label {
            font-size: 10px;
            color: #666;
            margin-bottom: 4px;
        }

        .outdoor-value {
            font-size: 20px;
            font-weight: 600;
        }

        .outdoor-value.orange {
            color: #f97316;
        }

        .outdoor-value.green {
            color: #22c55e;
        }

        .outdoor-value.green-dark {
            color: #16a34a;
        }

        /* Device Status */
        .device-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            width: 100%;
        }

        .status-item {
            text-align: center;
            flex: 1;
        }

        .status-item.center {
            display: flex;
            justify-content: center;
        }

        .status-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .status-value {
            font-size: 14px;
            color: #000;
            font-weight: 500;
        }

        .power-button {
            width: 40px;
            height: 40px;
            background: #ff8800;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .power-button:hover {
            background: #e67700;
            transform: scale(1.05);
        }

        .power-icon {
            width: 20px;
            height: 20px;
            border: 2px solid white;
            border-radius: 50%;
            position: relative;
        }

        .power-icon::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 10px;
            background: white;
        }

        /* Refresh Button */
        .refresh-btn {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: none;
            border: none;
            color: #999;
            font-size: 12px;
            cursor: pointer;
            padding: 8px;
        }

        .refresh-btn:hover {
            color: #666;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="time">9:41</div>
            <div class="battery-info">
                <div class="signal-bars">
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                </div>
                <div class="wifi-icon"></div>
                <div class="battery-icon"></div>
            </div>
        </div>

        <!-- Add Device Button -->
        <button class="add-device-btn">
            添加<br>设备
        </button>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Arc with Score -->
            <div class="arc-score-container">
                <div class="arc-container">
                    <svg width="300" height="200" viewBox="0 0 300 200">
                        <defs>
                            <linearGradient id="arcGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" stop-color="#ef4444" />
                                <stop offset="25%" stop-color="#f97316" />
                                <stop offset="50%" stop-color="#eab308" />
                                <stop offset="75%" stop-color="#22c55e" />
                                <stop offset="100%" stop-color="#06b6d4" />
                            </linearGradient>
                        </defs>
                        <path d="M 50 160 A 100 100 0 0 1 250 160" 
                              stroke="url(#arcGradient)" 
                              stroke-width="8" 
                              fill="none" 
                              stroke-linecap="round" />
                    </svg>
                </div>
                <div class="air-quality-score">
                    <div class="score-label">车内综合空气质量</div>
                    <div class="score-status">空气优</div>
                    <div class="score-number">25</div>
                </div>
            </div>

            <!-- Car Image -->
            <div class="car-container">
                <div class="car-image"></div>
            </div>

            <!-- Health Protection Info -->
            <div class="health-info">
                <div class="health-days">
                    已为您健康守护 <span class="number">231</span> 天
                </div>
                <div class="clean-reminder">
                    设置 <span class="highlight">清洗提醒</span>
                </div>
            </div>

            <!-- Air Quality Metrics -->
            <div class="air-metrics">
                <div class="metric-item">
                    <div class="metric-value">014</div>
                    <div class="metric-label">PM2.5(µg/m³）</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">36</div>
                    <div class="metric-label">甲醛(µg/m³）</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">25500</div>
                    <div class="metric-label">负氧离子(个/cm³）</div>
                </div>
            </div>

            <!-- Weather Card -->
            <div class="weather-card">
                <div class="weather-header">
                    <div class="weather-left">
                        <div class="weather-city">深圳市</div>
                        <div class="weather-update">2024-06-09 15:07 更新</div>
                    </div>
                    <div class="weather-temp">33</div>
                </div>
                <div class="weather-details">
                    <div class="weather-row">
                        <span class="weather-label">空气质量:</span>
                        <span class="weather-value">中</span>
                    </div>
                    <div class="weather-row">
                        <span class="weather-label">湿度:</span>
                        <span class="weather-value">55%</span>
                    </div>
                    <div class="weather-row">
                        <span class="weather-label">距离:</span>
                        <span class="weather-value">36km</span>
                    </div>
                </div>
            </div>

            <!-- Outdoor Air Quality Comparison -->
            <div class="outdoor-metrics">
                <div class="outdoor-item">
                    <div class="outdoor-label">车外PM2.5</div>
                    <div class="outdoor-value orange">105</div>
                </div>
                <div class="outdoor-item">
                    <div class="outdoor-label">车内PM2.5</div>
                    <div class="outdoor-value green">14</div>
                </div>
                <div class="outdoor-item">
                    <div class="outdoor-label">车外负氧离子</div>
                    <div class="outdoor-value green-dark">628</div>
                </div>
            </div>

            <!-- Device Status -->
            <div class="device-status">
                <div class="status-item">
                    <div class="status-label">设备运转</div>
                    <div class="status-value">正常</div>
                </div>
                <div class="status-item center">
                    <div class="power-button">
                        <div class="power-icon"></div>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-label">档位</div>
                    <div class="status-value">高</div>
                </div>
            </div>
        </div>

        <!-- Refresh Button -->
        <button class="refresh-btn">刷新</button>
    </div>
</body>
</html>
